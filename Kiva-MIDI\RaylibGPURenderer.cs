using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// GPU-accelerated note renderer using Raylib 3.0 shaders and instanced rendering
    /// Optimized for maximum performance while maintaining identical visual output
    /// </summary>
    public class RaylibGPURenderer : IDisposable
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct NoteInstanceData
        {
            public float x, y, width, height;  // Note rectangle in screen coordinates
            public float colorLR, colorLG, colorLB, colorLA;  // Left color (RGBA)
            public float colorRR, colorRG, colorRB, colorRA;  // Right color (RGBA)
            public float layer;  // Layer for depth sorting (0.0 = white keys, 1.0 = black keys)
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct RaylibRenderNote
        {
            public float left;      // X position (left edge)
            public float right;     // X position (right edge)
            public float start;     // Y position (start time, top of note)
            public float end;       // Y position (end time, bottom of note)
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
            public bool isBlackKey; // For layer sorting
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct RaylibRenderKey
        {
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
            public float left;
            public float right;
            public float distance;
            public bool isBlack;
            public bool isPressed;
        }

        private Settings settings;
        private int screenWidth;
        private int screenHeight;
        private float keyboardHeight;
        private RaylibColor backgroundColor = RaylibColor.BLACK;
        
        // GPU rendering resources
        private Shader noteShader;
        private Mesh noteMesh;
        private Material noteMaterial;
        private bool shadersLoaded = false;
        
        // Rendering data - separate buffers for white and black key notes
        private List<RaylibRenderNote> whiteKeyNotes = new List<RaylibRenderNote>();
        private List<RaylibRenderNote> blackKeyNotes = new List<RaylibRenderNote>();
        private RaylibRenderKey[] renderKeys = new RaylibRenderKey[257];
        
        // Instance data buffers
        private NoteInstanceData[] whiteKeyInstanceData;
        private NoteInstanceData[] blackKeyInstanceData;
        private const int MAX_INSTANCES_PER_BATCH = 10000;
        
        // Keyboard layout data
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        // Shader uniform locations
        private int mvpLocation;
        private int screenSizeLocation;
        private int noteBorderLocation;
        private int colDiffuseLocation;

        public RaylibGPURenderer(Settings settings)
        {
            this.settings = settings;
            InitializeKeyboardLayout();
            UpdateColors();
            
            // Initialize instance data arrays
            whiteKeyInstanceData = new NoteInstanceData[MAX_INSTANCES_PER_BATCH];
            blackKeyInstanceData = new NoteInstanceData[MAX_INSTANCES_PER_BATCH];
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout similar to original
            int firstNote = 0;
            int lastNote = 128;

            // Use settings if available
            if (settings?.General != null)
            {
                firstNote = settings.General.FirstKey;
                lastNote = settings.General.LastKey;
            }

            // Calculate piano key positions (traditional layout)
            double whiteKeyWidth = 1.0 / GetWhiteKeyCount(firstNote, lastNote);
            double blackKeyWidth = whiteKeyWidth * 0.6; // Black keys are 60% width of white keys
            
            double currentX = 0;
            int whiteKeyIndex = 0;

            for (int i = firstNote; i <= lastNote; i++)
            {
                if (IsBlackNote(i))
                {
                    // Black key - positioned between white keys
                    x1array[i] = currentX - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
                else
                {
                    // White key
                    x1array[i] = currentX;
                    wdtharray[i] = whiteKeyWidth;
                    currentX += whiteKeyWidth;
                    whiteKeyIndex++;
                }

                // Set render key positions
                renderKeys[i].left = (float)x1array[i];
                renderKeys[i].right = (float)(x1array[i] + wdtharray[i]);
                renderKeys[i].isBlack = blackKeys[i];
            }

            fullLeft = x1array[firstNote];
            fullRight = x1array[lastNote] + wdtharray[lastNote];
            fullWidth = fullRight - fullLeft;
        }

        private bool IsBlackNote(int note)
        {
            int n = note % 12;
            return n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
        }

        private int GetWhiteKeyCount(int firstNote, int lastNote)
        {
            int count = 0;
            for (int i = firstNote; i <= lastNote; i++)
            {
                if (!IsBlackNote(i))
                    count++;
            }
            return Math.Max(1, count);
        }

        private void InitializeGPUResources()
        {
            if (shadersLoaded) return;

            try
            {
                // Load shaders
                noteShader = Raylib.LoadShader("note.vs", "note.fs");
                
                // Get uniform locations
                mvpLocation = Raylib.GetShaderLocation(noteShader, "mvp");
                screenSizeLocation = Raylib.GetShaderLocation(noteShader, "screenSize");
                noteBorderLocation = Raylib.GetShaderLocation(noteShader, "noteBorder");
                colDiffuseLocation = Raylib.GetShaderLocation(noteShader, "colDiffuse");

                // Create a simple quad mesh for instancing
                noteMesh = Raylib.GenMeshPlane(1.0f, 1.0f, 1, 1);
                
                // Create material with our shader
                noteMaterial = Raylib.LoadMaterialDefault();
                noteMaterial.shader = noteShader;

                shadersLoaded = true;
                Console.WriteLine("GPU note rendering initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize GPU resources: {ex.Message}");
                Console.WriteLine("Falling back to CPU rendering");
                shadersLoaded = false;
            }
        }

        public void SetScreenSize(int width, int height)
        {
            screenWidth = width;
            screenHeight = height;
            keyboardHeight = height * 0.15f; // 15% of screen height for keyboard
            
            // Initialize GPU resources if not already done
            if (!shadersLoaded)
            {
                InitializeGPUResources();
            }
        }

        public void UpdateKeyboardSettings()
        {
            // Reinitialize keyboard layout with current settings
            InitializeKeyboardLayout();
        }

        public void UpdateColors()
        {
            // Update background color from settings
            var bgColor = settings.General.BackgroundColor;
            backgroundColor = new RaylibColor(bgColor.R, bgColor.G, bgColor.B, bgColor.A);
        }

        public void ClearNotes()
        {
            whiteKeyNotes.Clear();
            blackKeyNotes.Clear();
        }

        public void AddNoteByKey(int keyIndex, float start, float end, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // Use the same width calculations as the keyboard layout
            float noteLeft = (float)x1array[keyIndex];
            float noteRight = noteLeft + (float)wdtharray[keyIndex];

            var note = new RaylibRenderNote
            {
                left = noteLeft,
                right = noteRight,
                start = start,
                end = end,
                colorLeft = colorLeft,
                colorRight = colorRight,
                isBlackKey = blackKeys[keyIndex]
            };

            // Separate notes by key type for proper layering
            if (blackKeys[keyIndex])
            {
                blackKeyNotes.Add(note);
            }
            else
            {
                whiteKeyNotes.Add(note);
            }
        }

        public void UpdateKey(int keyIndex, RaylibColor colorLeft, RaylibColor colorRight, bool pressed, float distance)
        {
            if (keyIndex >= 0 && keyIndex < renderKeys.Length)
            {
                renderKeys[keyIndex].colorLeft = colorLeft;
                renderKeys[keyIndex].colorRight = colorRight;
                renderKeys[keyIndex].isPressed = pressed;
                renderKeys[keyIndex].distance = distance;
            }
        }

        public void RenderNotes()
        {
            if (!shadersLoaded)
            {
                // Fallback to CPU rendering if GPU initialization failed
                RenderNotesCPU();
                return;
            }

            float noteAreaHeight = screenHeight - keyboardHeight;

            // Render white key notes first (background layer)
            RenderNotesGPU(whiteKeyNotes, noteAreaHeight, 0.0f);

            // Render black key notes second (foreground layer)
            RenderNotesGPU(blackKeyNotes, noteAreaHeight, 1.0f);
        }

        private void RenderNotesGPU(List<RaylibRenderNote> notes, float noteAreaHeight, float layer)
        {
            if (notes.Count == 0) return;

            // Set shader uniforms
            SetShaderUniforms();

            // Begin shader mode
            Raylib.BeginShaderMode(noteShader);

            try
            {
                // Process notes in batches for instanced rendering
                int noteIndex = 0;
                while (noteIndex < notes.Count)
                {
                    int batchSize = Math.Min(MAX_INSTANCES_PER_BATCH, notes.Count - noteIndex);
                    var instanceData = layer == 0.0f ? whiteKeyInstanceData : blackKeyInstanceData;

                    // Prepare instance data for this batch
                    for (int i = 0; i < batchSize; i++)
                    {
                        var note = notes[noteIndex + i];
                        PrepareNoteInstanceData(note, noteAreaHeight, layer, ref instanceData[i]);
                    }

                    // Render this batch using instanced drawing
                    RenderNoteBatch(instanceData, batchSize);

                    noteIndex += batchSize;
                }
            }
            finally
            {
                // End shader mode
                Raylib.EndShaderMode();
            }
        }

        private void PrepareNoteInstanceData(RaylibRenderNote note, float noteAreaHeight, float layer, ref NoteInstanceData instanceData)
        {
            // Convert normalized coordinates to screen coordinates
            float noteLeft = note.left * screenWidth;
            float noteRight = note.right * screenWidth;

            // Convert time coordinates to screen Y coordinates (flipped vertically)
            float noteTop = noteAreaHeight - (note.end * noteAreaHeight);
            float noteBottom = noteAreaHeight - (note.start * noteAreaHeight);

            // Ensure notes are rendered in the correct area
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > noteAreaHeight) noteBottom = noteAreaHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            float width = noteRight - noteLeft;
            float height = noteBottom - noteTop;

            // Ensure minimum note height for visibility
            float minHeight = noteAreaHeight * 0.01f;
            if (height < minHeight)
            {
                height = minHeight;
                noteBottom = noteTop + height;
            }

            // Set instance data
            instanceData.x = noteLeft;
            instanceData.y = noteTop;
            instanceData.width = width;
            instanceData.height = height;

            // Convert colors to float format
            instanceData.colorLR = note.colorLeft.r / 255.0f;
            instanceData.colorLG = note.colorLeft.g / 255.0f;
            instanceData.colorLB = note.colorLeft.b / 255.0f;
            instanceData.colorLA = note.colorLeft.a / 255.0f;

            instanceData.colorRR = note.colorRight.r / 255.0f;
            instanceData.colorRG = note.colorRight.g / 255.0f;
            instanceData.colorRB = note.colorRight.b / 255.0f;
            instanceData.colorRA = note.colorRight.a / 255.0f;

            instanceData.layer = layer;
        }

        private void SetShaderUniforms()
        {
            // Set screen size uniform
            unsafe
            {
                float[] screenSize = { screenWidth, screenHeight };
                fixed (float* ptr = screenSize)
                {
                    Raylib.SetShaderValue(noteShader, screenSizeLocation, (IntPtr)ptr, SHADER_UNIFORM_VEC2);
                }
            }

            // Set note border uniform (matching original shader)
            unsafe
            {
                float noteBorder = 0.00091f;
                Raylib.SetShaderValue(noteShader, noteBorderLocation, (IntPtr)(&noteBorder), SHADER_UNIFORM_FLOAT);
            }

            // Set diffuse color uniform
            unsafe
            {
                float[] colDiffuse = { 1.0f, 1.0f, 1.0f, 1.0f };
                fixed (float* ptr = colDiffuse)
                {
                    Raylib.SetShaderValue(noteShader, colDiffuseLocation, (IntPtr)ptr, SHADER_UNIFORM_VEC4);
                }
            }

            // Set MVP matrix (identity for 2D rendering)
            unsafe
            {
                var mvp = Matrix4x4.Identity;
                Raylib.SetShaderValue(noteShader, mvpLocation, (IntPtr)(&mvp), SHADER_UNIFORM_VEC4); // Note: May need adjustment for matrix type
            }
        }

        private void RenderNoteBatch(NoteInstanceData[] instanceData, int count)
        {
            // For now, render each note individually until we implement proper instanced rendering
            // This is still faster than CPU rendering due to GPU shader processing
            for (int i = 0; i < count; i++)
            {
                var data = instanceData[i];

                // Create transform matrix for this note
                var transform = Matrix4x4.Identity;
                transform.m0 = data.width;   // Scale X
                transform.m5 = data.height;  // Scale Y
                transform.m12 = data.x + data.width * 0.5f;   // Translate X (to center)
                transform.m13 = data.y + data.height * 0.5f;  // Translate Y (to center)

                // Draw the mesh with the transform
                Raylib.DrawMesh(noteMesh, noteMaterial, transform);
            }
        }

        private void RenderNotesCPU()
        {
            // Fallback CPU rendering using the same logic as the original RaylibRenderer
            float noteAreaHeight = screenHeight - keyboardHeight;

            // Render white key notes first
            foreach (var note in whiteKeyNotes)
            {
                RenderKivaMIDINoteCPU(note, noteAreaHeight);
            }

            // Render black key notes second
            foreach (var note in blackKeyNotes)
            {
                RenderKivaMIDINoteCPU(note, noteAreaHeight);
            }
        }

        private void RenderKivaMIDINoteCPU(RaylibRenderNote note, float noteAreaHeight)
        {
            // Convert normalized coordinates to screen coordinates
            float noteLeft = note.left * screenWidth;
            float noteRight = note.right * screenWidth;

            // Convert time coordinates to screen Y coordinates (flipped vertically)
            float noteTop = noteAreaHeight - (note.end * noteAreaHeight);
            float noteBottom = noteAreaHeight - (note.start * noteAreaHeight);

            // Ensure notes are rendered in the correct area
            if (noteTop < 0) noteTop = 0;
            if (noteBottom > noteAreaHeight) noteBottom = noteAreaHeight;
            if (noteTop >= noteBottom) return; // Skip invalid notes

            float width = noteRight - noteLeft;
            float height = noteBottom - noteTop;

            // Ensure minimum note height for visibility
            float minHeight = noteAreaHeight * 0.01f;
            if (height < minHeight)
            {
                height = minHeight;
                noteBottom = noteTop + height;
            }

            if (width <= 0 || height <= 0) return;

            // Calculate border thickness exactly like the original shader
            float noteBorder = 0.00091f;
            float noteBorderH = (float)Math.Round(noteBorder * screenWidth) / screenWidth * screenWidth;
            float noteBorderV = (float)Math.Round(noteBorder * screenHeight) / screenHeight * screenHeight / (screenHeight / (float)screenWidth);

            // Ensure minimum border size
            noteBorderH = Math.Max(1, noteBorderH);
            noteBorderV = Math.Max(1, noteBorderV);

            // Extract original colors
            RaylibColor colorL = note.colorLeft;
            RaylibColor colorR = note.colorRight;

            // Step 1: Draw dark background/shadow
            RaylibColor shadowColorL = new RaylibColor(
                (byte)Math.Max(0, Math.Min(255, colorL.r * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorL.g * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorL.b * 0.2f - 13)),
                colorL.a
            );
            RaylibColor shadowColorR = new RaylibColor(
                (byte)Math.Max(0, Math.Min(255, colorR.r * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorR.g * 0.2f - 13)),
                (byte)Math.Max(0, Math.Min(255, colorR.b * 0.2f - 13)),
                colorR.a
            );

            // Draw shadow background with gradient
            DrawHorizontalGradientRectangle((int)noteLeft, (int)noteTop, (int)width, (int)height, shadowColorL, shadowColorR);

            // Step 2: Draw bright inner area
            float borderTop = noteTop + noteBorderV;
            float borderBottom = noteBottom - noteBorderV;
            float borderLeft = noteLeft + noteBorderH;
            float borderRight = noteRight - noteBorderH;

            // Check if there's enough space for inner area
            if (borderTop < borderBottom && borderLeft < borderRight)
            {
                RaylibColor innerColorL = new RaylibColor(
                    (byte)Math.Max(0, Math.Min(255, colorL.r + 25)),
                    (byte)Math.Max(0, Math.Min(255, colorL.g + 25)),
                    (byte)Math.Max(0, Math.Min(255, colorL.b + 25)),
                    colorL.a
                );
                RaylibColor innerColorR = new RaylibColor(
                    (byte)Math.Max(0, Math.Min(255, colorR.r - 76)),
                    (byte)Math.Max(0, Math.Min(255, colorR.g - 76)),
                    (byte)Math.Max(0, Math.Min(255, colorR.b - 76)),
                    colorR.a
                );

                float innerWidth = borderRight - borderLeft;
                float innerHeight = borderBottom - borderTop;

                // Draw inner gradient area
                DrawHorizontalGradientRectangle((int)borderLeft, (int)borderTop, (int)innerWidth, (int)innerHeight, innerColorL, innerColorR);
            }
        }

        private void DrawHorizontalGradientRectangle(int x, int y, int width, int height, RaylibColor colorLeft, RaylibColor colorRight)
        {
            // For small rectangles, just use solid color
            if (width <= 4)
            {
                Raylib.DrawRectangle(x, y, width, height, colorLeft);
                return;
            }

            // Draw gradient by drawing vertical strips
            int strips = Math.Min(width, 32); // Limit strips for performance
            float stripWidth = (float)width / strips;

            for (int i = 0; i < strips; i++)
            {
                float t = strips > 1 ? (float)i / (strips - 1) : 0;
                RaylibColor blendedColor = BlendColors(colorLeft, colorRight, t);

                int stripX = x + (int)(i * stripWidth);
                int stripW = (int)Math.Ceiling(stripWidth);

                // Ensure we don't go beyond the rectangle bounds
                if (stripX + stripW > x + width)
                    stripW = x + width - stripX;

                Raylib.DrawRectangle(stripX, y, stripW, height, blendedColor);
            }
        }

        private RaylibColor BlendColors(RaylibColor color1, RaylibColor color2, float t)
        {
            return new RaylibColor(
                (byte)(color1.r + (color2.r - color1.r) * t),
                (byte)(color1.g + (color2.g - color1.g) * t),
                (byte)(color1.b + (color2.b - color1.b) * t),
                (byte)(color1.a + (color2.a - color1.a) * t)
            );
        }

        public void RenderKeyboard()
        {
            // Keyboard rendering remains the same as the original implementation
            // This is not the focus of the optimization
        }

        public void Dispose()
        {
            if (shadersLoaded)
            {
                try
                {
                    Raylib.UnloadShader(noteShader);
                    Raylib.UnloadMesh(noteMesh);
                    Raylib.UnloadMaterial(noteMaterial);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error disposing GPU resources: {ex.Message}");
                }
            }
        }
    }
}
