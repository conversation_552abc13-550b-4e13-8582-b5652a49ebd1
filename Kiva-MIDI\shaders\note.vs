#version 330

// Vertex attributes
in vec3 vertexPosition;    // Base quad vertex position (0,0 to 1,1)
in vec2 vertexTexCoord;    // Texture coordinates (0,0 to 1,1)

// Instance attributes (per-note data)
in vec4 noteRect;          // x=left, y=top, z=right, w=bottom (screen coordinates)
in vec4 colorLeft;         // Left side color (RGBA)
in vec4 colorRight;        // Right side color (RGBA)

// Uniforms
uniform mat4 mvp;          // Model-View-Projection matrix
uniform vec2 screenSize;   // Screen width and height

// Outputs to fragment shader
out vec2 fragTexCoord;     // Texture coordinates (0,0 to 1,1 within note)
out vec4 fragColorLeft;    // Left side color
out vec4 fragColorRight;   // Right side color
out vec2 noteSize;         // Note width and height in pixels
out vec2 screenRes;        // Screen resolution for border calculations

void main() {
    // Pass through colors and screen info
    fragColorLeft = colorLeft;
    fragColorRight = colorRight;
    fragTexCoord = vertexTexCoord;
    screenRes = screenSize;
    
    // Calculate note dimensions
    float noteWidth = noteRect.z - noteRect.x;
    float noteHeight = noteRect.w - noteRect.y;
    noteSize = vec2(noteWidth, noteHeight);
    
    // Transform vertex position to note's screen space
    vec2 notePos = vec2(
        noteRect.x + vertexPosition.x * noteWidth,
        noteRect.y + vertexPosition.y * noteHeight
    );
    
    // Convert to normalized device coordinates (-1 to 1)
    vec2 ndc = (notePos / screenSize) * 2.0 - 1.0;
    
    // Flip Y coordinate to match Raylib's coordinate system
    ndc.y = -ndc.y;
    
    gl_Position = vec4(ndc, 0.0, 1.0);
}
