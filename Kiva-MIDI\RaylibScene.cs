using System;
using System.Drawing;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Raylib-based scene to replace DirectX scene
    /// </summary>
    public class RaylibScene : IDisposable
    {
        private RaylibRenderer renderer;
        private RaylibMIDIRenderer midiRenderer;
        private RaylibTextureRenderer textureRenderer; // Added texture renderer
        private Settings settings;
        private bool isInitialized = false;
        private int windowWidth = 800;
        private int windowHeight = 600;
        private bool useTextureRenderer = true; // Flag to enable texture-based rendering

        public Settings Settings 
        { 
            get => settings; 
            set => settings = value; 
        }

        public MIDIFile File
        {
            get => midiRenderer?.File;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.File = value;
            }
        }

        public PlayingState Time
        {
            get => midiRenderer?.Time;
            set
            {
                if (midiRenderer != null)
                    midiRenderer.Time = value;
            }
        }

        public long LastRenderedNoteCount => midiRenderer?.LastRenderedNoteCount ?? 0;
        public long LastNPS => midiRenderer?.LastNPS ?? 0;
        public long LastPolyphony => midiRenderer?.LastPolyphony ?? 0;
        public long NotesPassedSum => midiRenderer?.NotesPassedSum ?? 0;

        // Property to check if texture renderer is enabled
        public bool IsTextureRendererEnabled => useTextureRenderer;

        // Public access to renderer for settings updates
        public RaylibRenderer Renderer => renderer;

        public RaylibScene(Settings settings)
        {
            this.settings = settings;
        }

        public void Initialize(int width, int height, string title)
        {
            if (isInitialized)
                return;

            windowWidth = width;
            windowHeight = height;

            Console.WriteLine("Initializing raylib window...");
            Raylib.SetConfigFlags(ConfigFlags.FLAG_WINDOW_RESIZABLE);
            Raylib.InitWindow(width, height, title);
            Raylib.SetTargetFPS(settings.General.FPSLock);

            Console.WriteLine("Creating renderer...");
            renderer = new RaylibRenderer(settings);
            renderer.SetScreenSize(width, height);

            Console.WriteLine("Creating MIDI renderer...");
            midiRenderer = new RaylibMIDIRenderer(renderer, settings);

            // Calculate keyboard height (same as in RaylibRenderer)
            float keyboardHeight = height * 0.2f; // 20% of screen height for keyboard

            Console.WriteLine("Creating texture renderer...");
            textureRenderer = new RaylibTextureRenderer(width, height, keyboardHeight);

            isInitialized = true;
            Console.WriteLine("Raylib initialization complete.");
        }

        public void RenderMIDIContent()
        {
            if (!isInitialized)
                return;

            // Handle window resize
            int currentWidth = Raylib.GetScreenWidth();
            int currentHeight = Raylib.GetScreenHeight();
            if (currentWidth != windowWidth || currentHeight != windowHeight)
            {
                windowWidth = currentWidth;
                windowHeight = currentHeight;
                renderer?.SetScreenSize(windowWidth, windowHeight);

                // Recreate texture renderer with new dimensions
                if (textureRenderer != null)
                {
                    textureRenderer.Dispose();
                    float keyboardHeight = windowHeight * 0.2f;
                    textureRenderer = new RaylibTextureRenderer(windowWidth, windowHeight, keyboardHeight);
                }
            }

            try
            {
                // Get current playback time
                double currentTime = Time?.GetTime() ?? 0;
                double timeScale = settings?.Volatile?.Size ?? 1.0;
                double renderCutoff = currentTime + timeScale;

                // Begin frame
                renderer?.BeginFrame();

                // Process MIDI file for rendering
                if (File != null && File is MIDIMemoryFile memoryFile)
                {
                    try
                    {
                        // Update texture renderer with current time
                        if (useTextureRenderer && textureRenderer != null)
                        {
                            textureRenderer.UpdatePlayTime((float)currentTime);

                            // Update note states in texture renderer
                            UpdateTextureRendererNotes(memoryFile, currentTime, memoryFile.MidiNoteColors);

                            // Update and draw the texture
                            textureRenderer.UpdateNoteTexture();
                            textureRenderer.DrawNoteTexture();
                        }
                        else
                        {
                            // Use traditional rendering if texture renderer is disabled
                            RenderMIDIFile(memoryFile, currentTime, timeScale, renderCutoff);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error rendering MIDI file: {ex.Message}");
                    }
                }

                // Always render notes and keyboard
                if (!useTextureRenderer)
                {
                    renderer.RenderNotes();
                }

                // Always render the keyboard
                renderer.RenderKeyboard();

                // End frame
                renderer?.EndFrame();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MIDI render error: {ex.Message}");
            }
        }

        public bool ShouldClose()
        {
            return !isInitialized || Raylib.WindowShouldClose();
        }

        public void SetScreenSize(int width, int height)
        {
            windowWidth = width;
            windowHeight = height;
            renderer?.SetScreenSize(width, height);

            // Recreate texture renderer with new dimensions
            if (textureRenderer != null)
            {
                textureRenderer.Dispose();
                float keyboardHeight = height * 0.2f;
                textureRenderer = new RaylibTextureRenderer(width, height, keyboardHeight);
            }
        }

        // Reset the texture renderer (clear all notes and texture)
        public void ResetTextureRenderer()
        {
            if (textureRenderer != null)
            {
                // Reset all notes
                for (int i = 0; i < 128; i++)
                {
                    textureRenderer.SetNoteOff(i);
                }

                // Reset texture (will be redrawn on next update)
                textureRenderer.ResetTexture();
            }
        }

        // Toggle between texture rendering and traditional rendering
        public void ToggleTextureRenderer()
        {
            useTextureRenderer = !useTextureRenderer;
            Console.WriteLine($"Texture renderer: {(useTextureRenderer ? "Enabled" : "Disabled")}");

            if (useTextureRenderer && textureRenderer != null)
            {
                ResetTextureRenderer();
            }
        }

        public void Dispose()
        {
            if (!isInitialized)
                return;

            midiRenderer?.Dispose();
            renderer?.Dispose();
            textureRenderer?.Dispose();

            if (Raylib.IsWindowReady())
            {
                Raylib.CloseWindow();
            }

            isInitialized = false;
        }
    }

    /// <summary>
    /// MIDI-specific renderer using raylib
    /// </summary>
    public class RaylibMIDIRenderer : IDisposable
    {
        private RaylibRenderer renderer;
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private object fileLock = new object();

        // Statistics
        public long LastRenderedNoteCount { get; private set; } = 0;
        public long LastNPS { get; private set; } = 0;
        public long LastPolyphony { get; private set; } = 0;
        public long NotesPassedSum { get; private set; } = 0;

        // Keyboard layout
        private bool[] blackKeys = new bool[257];
        private double[] x1array = new double[257];
        private double[] wdtharray = new double[257];
        private double fullLeft, fullRight, fullWidth;

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (fileLock)
                {
                    file = value;
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public RaylibMIDIRenderer(RaylibRenderer renderer, Settings settings)
        {
            this.renderer = renderer;
            this.settings = settings;
            InitializeKeyboardLayout();
        }

        private void InitializeKeyboardLayout()
        {
            // Initialize black key pattern
            for (int i = 0; i < blackKeys.Length; i++)
            {
                blackKeys[i] = IsBlackNote(i);
            }

            // Calculate keyboard layout
            CalculateKeyPositions();
        }

        private void CalculateKeyPositions()
        {
            int firstNote = 0;
            int lastNote = 128;
            
            // Apply key range settings
            if (settings.General.KeyRange == KeyRangeTypes.Key88)
            {
                firstNote = 21;
                lastNote = 109;
            }
            else if (settings.General.KeyRange == KeyRangeTypes.Key256)
            {
                firstNote = 0;
                lastNote = 256;
            }

            // Calculate white key positions
            int whiteKeyCount = 0;
            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i]) whiteKeyCount++;
            }

            double whiteKeyWidth = 1.0 / whiteKeyCount;
            int whiteKeyIndex = 0;

            fullLeft = 0;
            fullRight = 1;
            fullWidth = 1;

            for (int i = firstNote; i < lastNote; i++)
            {
                if (!blackKeys[i])
                {
                    // White key
                    x1array[i] = whiteKeyIndex * whiteKeyWidth;
                    wdtharray[i] = whiteKeyWidth;
                    whiteKeyIndex++;
                }
                else
                {
                    // Black key
                    double blackKeyWidth = whiteKeyWidth * 0.6;
                    x1array[i] = (whiteKeyIndex - 0.5) * whiteKeyWidth - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
            }
        }

        public void Render()
        {
            if (time == null || renderer == null)
                return;

            double currentTime = time.GetTime();
            double timeScale = settings?.Volatile?.Size ?? 1.0;
            double renderCutoff = currentTime + timeScale;

            lock (fileLock)
            {
                if (file != null && file is MIDIMemoryFile memoryFile)
                {
                    try
                    {
                        RenderMIDIFile(memoryFile, currentTime, timeScale, renderCutoff);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error rendering MIDI file: {ex.Message}");
                    }
                }
            }

            // Always render notes first, then keyboard on top
            try
            {
                renderer.RenderNotes();
                renderer.RenderKeyboard();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering keyboard/notes: {ex.Message}");
            }
        }

        private void RenderMIDIFile(MIDIMemoryFile file, double currentTime, double timeScale, double renderCutoff)
        {
            if (file == null || renderer == null)
                return;

            try
            {
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            var colors = file.MidiNoteColors;
            if (colors == null)
            {
                Console.WriteLine("Warning: MidiNoteColors is null");
                return;
            }

            long notesRendered = 0;
            int polyphony = 0;

            // Clear all key states first
            for (int k = 0; k < 256; k++)
            {
                renderer.UpdateKey(k, new RaylibColor(0, 0, 0, 0), new RaylibColor(0, 0, 0, 0), false, 0);
            }

            // Update texture renderer with note states if enabled
            if (useTextureRenderer && textureRenderer != null)
            {
                UpdateTextureRendererNotes(file, currentTime, colors);
            }

            // Render white key notes first (background layer)
            for (int k = 0; k < 256; k++)
            {
                if (blackKeys[k]) continue; // Skip black keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            // Render black key notes second (foreground layer) - they will overlap white key notes
            for (int k = 0; k < 256; k++)
            {
                if (!blackKeys[k]) continue; // Skip white keys in this pass
                RenderNotesForKey(file, k, currentTime, timeScale, renderCutoff, colors, ref notesRendered, ref polyphony);
            }

            LastRenderedNoteCount = notesRendered;
            LastPolyphony = polyphony;
        }

        // Update the texture renderer with note states from the MIDI file
        private void UpdateTextureRendererNotes(MIDIMemoryFile file, double currentTime, NoteCol[] colors)
        {
            if (file == null || textureRenderer == null || colors == null)
                return;

            try
            {
                // Make sure colors are up to date
                file.SetColorEvents(currentTime);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting color events: {ex.Message}");
                return;
            }

            // Process all notes to update texture renderer
            for (int k = 0; k < 128; k++) // Only process standard MIDI range (0-127)
            {
                if (file.Notes == null || k >= file.Notes.Length)
                    continue;

                var notes = file.Notes[k];
                if (notes == null || notes.Length == 0)
                    continue;

                // Check if any note is currently active
                bool isNoteActive = false;
                foreach (var note in notes)
                {
                    // If note is currently active
                    if (note.start <= currentTime && note.end >= currentTime)
                    {
                        isNoteActive = true;

                        // Get note color from the MIDI file
                        var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                        // Set note on in texture renderer with start time
                        textureRenderer.SetNoteOn(k, 0, 127, 0, noteColor);

                        // Update the note's start time to match the MIDI file
                        textureRenderer.SetNoteStartTime(k, (float)note.start);
                        break;
                    }
                }

                // If no active notes found, check if any notes just ended
                if (!isNoteActive)
                {
                    // Find the most recently ended note
                    double mostRecentEnd = 0;
                    foreach (var note in notes)
                    {
                        if (note.end <= currentTime && note.end > mostRecentEnd)
                        {
                            mostRecentEnd = note.end;
                        }
                    }

                    // If a note just ended (within the last 0.5 seconds), set its release time
                    if (mostRecentEnd > 0 && currentTime - mostRecentEnd < 0.5)
                    {
                        textureRenderer.SetNoteReleaseTime(k, (float)mostRecentEnd);
                    }
                    else
                    {
                        // Otherwise, turn the note off completely
                        textureRenderer.SetNoteOff(k);
                    }
                }
            }
        }

        private void RenderNotesForKey(MIDIMemoryFile file, int k, double currentTime, double timeScale, double renderCutoff, NoteCol[] colors, ref long notesRendered, ref int polyphony)
        {
            if (file.Notes == null || k >= file.Notes.Length)
                return;

            var notes = file.Notes[k];
            if (notes == null || notes.Length == 0)
                return;

            // Use original piano layout positioning
            float left = (float)((x1array[k] - fullLeft) / fullWidth);
            float right = (float)((x1array[k] + wdtharray[k] - fullLeft) / fullWidth);
            bool pressed = false;
            var keyColor = new RaylibColor(0, 0, 0, 0);

            // Process notes for this key
            foreach (var note in notes)
            {
                if (note.end < currentTime - 0.1) // Small buffer for note ending
                    continue;
                if (note.start > renderCutoff)
                    break;

                // Check if key is currently pressed
                if (note.start <= currentTime && note.end >= currentTime)
                {
                    pressed = true;
                    polyphony++;

                    // Get note color
                    var noteCol = colors[note.colorPointer];
                    keyColor = ColorFromNoteCol(noteCol);
                }

                // Add note to render buffer if it's visible
                if (note.start <= renderCutoff && note.end >= currentTime)
                {
                    float noteStart = (float)((note.start - currentTime) / timeScale);
                    float noteEnd = (float)((note.end - currentTime) / timeScale);
                    var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                    // Use AddNoteByKey to get proper piano key widths
                    renderer.AddNoteByKey(k, noteStart, noteEnd, noteColor, noteColor);
                    notesRendered++;
                }
            }

            // Update key state
            renderer.UpdateKey(k, keyColor, keyColor, pressed, 0);
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        private bool IsBlackNote(int noteNumber)
        {
            int n = noteNumber % 12;
            return n == 1 || n == 3 || n == 6 || n == 8 || n == 10;
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
