using System;
using System.Collections.Generic;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Texture-based note renderer that implements the sliding texture system from midi_visualizer.c
    /// while maintaining <PERSON><PERSON>'s visual style and piano keyboard layout
    /// </summary>
    public class RaylibTextureRenderer : IDisposable
    {
        private const int MAX_MIDI_NOTES = 128;
        private const float SECONDS_BUFFER = 1.0f;
        
        // Texture properties
        private RenderTexture2D noteTexture;
        private int textureWidth;
        private int textureHeight;
        private float pixelsPerSecond;
        private float textureScrollPos;
        private float lastUpdateTime;
        
        // Screen properties
        private int screenWidth;
        private int screenHeight;
        private float keyboardHeight;
        
        // Note state tracking (similar to midi_visualizer.c)
        private NoteState[] notes;
        private float currentPlayTime;
        
        // Piano keyboard layout (from existing Kiva system)
        private double[] x1array;
        private double[] wdtharray;
        private double fullLeft;
        private double fullWidth;
        private bool[] blackKeys;
        
        public struct NoteState
        {
            public bool active;
            public float startTime;
            public float releaseTime;
            public int channel;
            public int note;
            public int velocity;
            public int trackIndex;
            public RaylibColor color;
        }
        
        public RaylibTextureRenderer(int screenWidth, int screenHeight, float keyboardHeight)
        {
            this.screenWidth = screenWidth;
            this.screenHeight = screenHeight;
            this.keyboardHeight = keyboardHeight;
            
            // Calculate texture dimensions
            this.textureWidth = screenWidth * 2; // Double width for scrolling
            this.textureHeight = (int)(screenHeight - keyboardHeight);
            this.pixelsPerSecond = textureWidth / SECONDS_BUFFER;
            
            // Initialize note states
            notes = new NoteState[MAX_MIDI_NOTES];
            for (int i = 0; i < MAX_MIDI_NOTES; i++)
            {
                notes[i] = new NoteState
                {
                    active = false,
                    startTime = 0.0f,
                    releaseTime = 0.0f,
                    channel = 0,
                    note = i,
                    velocity = 0,
                    trackIndex = 0,
                    color = RaylibColor.WHITE
                };
            }
            
            // Initialize piano keyboard layout (using Kiva's existing layout)
            InitializePianoLayout();
            
            // Create the render texture
            InitializeNoteTexture();
        }
        
        private void InitializePianoLayout()
        {
            // Initialize the piano keyboard layout arrays (same as existing Kiva system)
            x1array = new double[128];
            wdtharray = new double[128];
            blackKeys = new bool[128];
            
            // Standard piano layout calculation
            double whiteKeyWidth = screenWidth / 75.0; // 75 white keys in full range
            double blackKeyWidth = whiteKeyWidth * 0.6;
            
            int whiteKeyIndex = 0;
            fullLeft = 0;
            
            for (int i = 0; i < 128; i++)
            {
                int noteInOctave = i % 12;
                bool isBlack = (noteInOctave == 1 || noteInOctave == 3 || noteInOctave == 6 || 
                               noteInOctave == 8 || noteInOctave == 10);
                
                blackKeys[i] = isBlack;
                
                if (isBlack)
                {
                    // Black key positioning
                    double prevWhiteX = whiteKeyIndex > 0 ? (whiteKeyIndex - 1) * whiteKeyWidth : 0;
                    x1array[i] = prevWhiteX + whiteKeyWidth - blackKeyWidth / 2;
                    wdtharray[i] = blackKeyWidth;
                }
                else
                {
                    // White key positioning
                    x1array[i] = whiteKeyIndex * whiteKeyWidth;
                    wdtharray[i] = whiteKeyWidth;
                    whiteKeyIndex++;
                }
            }
            
            fullWidth = whiteKeyIndex * whiteKeyWidth;
        }
        
        private void InitializeNoteTexture()
        {
            // Create the render texture for notes
            noteTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
            
            // Clear the texture
            Raylib.BeginTextureMode(noteTexture);
            Raylib.ClearBackground(RaylibColor.BLACK);
            Raylib.EndTextureMode();
            
            textureScrollPos = 0.0f;
            lastUpdateTime = 0.0f;
        }
        
        public void UpdatePlayTime(float playTime)
        {
            currentPlayTime = playTime;
        }
        
        public void SetNoteOn(int note, int channel, int velocity, int trackIndex, RaylibColor color)
        {
            if (note >= 0 && note < MAX_MIDI_NOTES)
            {
                notes[note].active = true;
                notes[note].startTime = currentPlayTime;
                notes[note].releaseTime = -1; // Not released yet
                notes[note].channel = channel;
                notes[note].velocity = velocity;
                notes[note].trackIndex = trackIndex;
                notes[note].color = color;
            }
        }
        
        public void SetNoteOff(int note)
        {
            if (note >= 0 && note < MAX_MIDI_NOTES && notes[note].active)
            {
                notes[note].active = false;
                notes[note].releaseTime = currentPlayTime;
            }
        }
        
        public void UpdateNoteTexture()
        {
            // Calculate how much time has passed since last update
            float timeDelta = currentPlayTime - lastUpdateTime;
            float pixelsDelta = timeDelta * pixelsPerSecond;
            
            // Skip update if delta is too small (less than 1 pixel)
            if (pixelsDelta < 1.0f)
            {
                return;
            }
            
            // Begin drawing to texture
            Raylib.BeginTextureMode(noteTexture);
            
            // Clear the area ahead where new notes will be drawn
            Raylib.DrawRectangle((int)textureScrollPos, 0, (int)(pixelsDelta + 1), textureHeight, RaylibColor.BLACK);
            
            // Render notes to texture (similar to midi_visualizer.c approach)
            RenderNotesToTexture();
            
            Raylib.EndTextureMode();
            
            // Update scroll position
            textureScrollPos += pixelsDelta;
            
            // If we've scrolled completely past the window, reset the scroll position
            if (textureScrollPos >= textureWidth / 2)
            {
                textureScrollPos -= textureWidth / 2;
            }
            
            // Update last update time
            lastUpdateTime = currentPlayTime;
        }
        
        private void RenderNotesToTexture()
        {
            // Pre-calculate constants (similar to midi_visualizer.c)
            const int noteHeight = 4;

            // Process all notes and render them to texture
            for (int i = 0; i < MAX_MIDI_NOTES; i++)
            {
                var note = notes[i];

                // Skip if note is not active and not recently released
                if (!note.active && note.releaseTime <= 0) continue;

                // Calculate note position in texture coordinates (similar to midi_visualizer.c)
                float startX = (note.startTime - currentPlayTime) * pixelsPerSecond + textureScrollPos;
                float endX;

                if (note.active)
                {
                    // Note is still active, extend to current time
                    endX = textureScrollPos;
                }
                else
                {
                    // Note has been released
                    endX = (note.releaseTime - currentPlayTime) * pixelsPerSecond + textureScrollPos;
                }

                // Only render if visible in the current texture window
                if (startX < textureWidth && endX > 0)
                {
                    // Calculate Y position (flipped vertically like Kiva)
                    int yPos = textureHeight - ((i + 1) * noteHeight);

                    // Ensure width is at least 1 pixel
                    float width = Math.Max(endX - startX, 1.0f);

                    // Calculate note width using Kiva's piano layout
                    float noteLeft = (float)((x1array[i] - fullLeft) / fullWidth) * screenWidth;
                    float noteWidth = (float)(wdtharray[i] / fullWidth) * screenWidth;

                    // Draw the note with Kiva's visual style
                    DrawKivaStyleNote(startX, yPos, width, noteHeight, note.color);
                }
            }
        }
        
        private void DrawKivaStyleNote(float x, float y, float width, float height, RaylibColor color)
        {
            // Draw note with border (similar to Kiva's style)
            float borderSize = 1.0f;
            
            // Draw darker border
            RaylibColor borderColor = new RaylibColor(
                (byte)(color.r * 0.2f),
                (byte)(color.g * 0.2f),
                (byte)(color.b * 0.2f),
                color.a
            );
            
            Raylib.DrawRectangle((int)(x - borderSize), (int)(y - borderSize), 
                               (int)(width + 2 * borderSize), (int)(height + 2 * borderSize), borderColor);
            
            // Draw main note
            Raylib.DrawRectangle((int)x, (int)y, (int)width, (int)height, color);
        }
        
        public void DrawNoteTexture()
        {
            // Draw the texture with proper scrolling (similar to midi_visualizer.c)
            // Draw the first part of the texture
            Rectangle source1 = new Rectangle(textureScrollPos, 0, textureWidth / 2 - textureScrollPos, textureHeight);
            Rectangle dest1 = new Rectangle(0, 0, textureWidth / 2 - textureScrollPos, textureHeight);
            Raylib.DrawTexturePro(noteTexture.texture, source1, dest1, new Vector2(0, 0), 0.0f, RaylibColor.WHITE);
            
            // If we're close to wrapping, draw the beginning of the texture at the right edge
            if (textureScrollPos > 0)
            {
                Rectangle source2 = new Rectangle(0, 0, textureScrollPos, textureHeight);
                Rectangle dest2 = new Rectangle(textureWidth / 2 - textureScrollPos, 0, textureScrollPos, textureHeight);
                Raylib.DrawTexturePro(noteTexture.texture, source2, dest2, new Vector2(0, 0), 0.0f, RaylibColor.WHITE);
            }
        }
        
        // Reset the texture (clear all content)
        public void ResetTexture()
        {
            // Clear the texture
            Raylib.BeginTextureMode(noteTexture);
            Raylib.ClearBackground(RaylibColor.BLACK);
            Raylib.EndTextureMode();

            // Reset scroll position and timing
            textureScrollPos = 0.0f;
            lastUpdateTime = currentPlayTime;
        }

        public void Dispose()
        {
            if (noteTexture.id != 0)
            {
                Raylib.UnloadRenderTexture(noteTexture);
            }
        }
    }
}
