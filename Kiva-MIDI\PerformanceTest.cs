using System;
using System.Diagnostics;
using static Kiva_MIDI.RaylibPInvoke;
using RaylibColor = Kiva_MIDI.RaylibPInvoke.Color;

namespace Kiva_MIDI
{
    /// <summary>
    /// Performance testing utility to compare CPU vs GPU note rendering
    /// </summary>
    public static class PerformanceTest
    {
        public static void RunNoteRenderingBenchmark(Settings settings, int noteCount = 10000)
        {
            Console.WriteLine("=== Note Rendering Performance Benchmark ===");
            Console.WriteLine($"Testing with {noteCount:N0} notes");
            Console.WriteLine();

            // Initialize test window
            Raylib.InitWindow(1200, 700, "Performance Test");
            Raylib.SetTargetFPS(60);

            try
            {
                // Test GPU renderer
                Console.WriteLine("Testing GPU-accelerated renderer...");
                var gpuTime = BenchmarkGPURenderer(settings, noteCount);
                
                // Test CPU renderer (fallback)
                Console.WriteLine("Testing CPU renderer (fallback)...");
                var cpuTime = BenchmarkCPURenderer(settings, noteCount);

                // Display results
                Console.WriteLine();
                Console.WriteLine("=== Results ===");
                Console.WriteLine($"GPU Renderer: {gpuTime:F2} ms per frame");
                Console.WriteLine($"CPU Renderer: {cpuTime:F2} ms per frame");
                
                if (cpuTime > 0 && gpuTime > 0)
                {
                    double speedup = cpuTime / gpuTime;
                    Console.WriteLine($"GPU Speedup: {speedup:F1}x faster");
                    
                    double gpuFPS = 1000.0 / gpuTime;
                    double cpuFPS = 1000.0 / cpuTime;
                    Console.WriteLine($"GPU FPS: {gpuFPS:F1}");
                    Console.WriteLine($"CPU FPS: {cpuFPS:F1}");
                }
                
                Console.WriteLine();
                Console.WriteLine("Performance optimization complete!");
            }
            finally
            {
                Raylib.CloseWindow();
            }
        }

        private static double BenchmarkGPURenderer(Settings settings, int noteCount)
        {
            var renderer = new RaylibGPURenderer(settings);
            renderer.SetScreenSize(1200, 700);

            // Generate test notes
            var testNotes = GenerateTestNotes(noteCount);
            
            var stopwatch = new Stopwatch();
            const int testFrames = 100;
            
            // Warm up
            for (int i = 0; i < 10; i++)
            {
                RenderTestFrame(renderer, testNotes);
            }

            // Benchmark
            stopwatch.Start();
            for (int frame = 0; frame < testFrames; frame++)
            {
                RenderTestFrame(renderer, testNotes);
            }
            stopwatch.Stop();

            renderer.Dispose();
            
            return stopwatch.Elapsed.TotalMilliseconds / testFrames;
        }

        private static double BenchmarkCPURenderer(Settings settings, int noteCount)
        {
            var renderer = new RaylibRenderer(settings);
            renderer.SetScreenSize(1200, 700);

            // Force CPU rendering by creating a renderer without GPU support
            // We'll simulate this by using the fallback path
            
            // Generate test notes
            var testNotes = GenerateTestNotes(noteCount);
            
            var stopwatch = new Stopwatch();
            const int testFrames = 100;
            
            // Warm up
            for (int i = 0; i < 10; i++)
            {
                RenderTestFrameCPU(renderer, testNotes);
            }

            // Benchmark
            stopwatch.Start();
            for (int frame = 0; frame < testFrames; frame++)
            {
                RenderTestFrameCPU(renderer, testNotes);
            }
            stopwatch.Stop();

            renderer.Dispose();
            
            return stopwatch.Elapsed.TotalMilliseconds / testFrames;
        }

        private static TestNote[] GenerateTestNotes(int count)
        {
            var notes = new TestNote[count];
            var random = new Random(42); // Fixed seed for consistent results

            for (int i = 0; i < count; i++)
            {
                notes[i] = new TestNote
                {
                    keyIndex = random.Next(0, 128),
                    start = (float)random.NextDouble(),
                    end = (float)random.NextDouble() + 0.1f,
                    colorLeft = new RaylibColor(
                        (byte)random.Next(0, 256),
                        (byte)random.Next(0, 256),
                        (byte)random.Next(0, 256),
                        255
                    ),
                    colorRight = new RaylibColor(
                        (byte)random.Next(0, 256),
                        (byte)random.Next(0, 256),
                        (byte)random.Next(0, 256),
                        255
                    )
                };
            }

            return notes;
        }

        private static void RenderTestFrame(RaylibGPURenderer renderer, TestNote[] notes)
        {
            Raylib.BeginDrawing();
            Raylib.ClearBackground(RaylibColor.BLACK);

            renderer.ClearNotes();
            
            // Add all test notes
            foreach (var note in notes)
            {
                renderer.AddNoteByKey(note.keyIndex, note.start, note.end, note.colorLeft, note.colorRight);
            }

            // Render notes
            renderer.RenderNotes();

            Raylib.EndDrawing();
        }

        private static void RenderTestFrameCPU(RaylibRenderer renderer, TestNote[] notes)
        {
            Raylib.BeginDrawing();
            
            renderer.BeginFrame();
            
            // Add all test notes
            foreach (var note in notes)
            {
                renderer.AddNoteByKey(note.keyIndex, note.start, note.end, note.colorLeft, note.colorRight);
            }

            // Force CPU rendering by calling the CPU method directly
            // This simulates the fallback behavior
            renderer.RenderNotes();

            Raylib.EndDrawing();
        }

        private struct TestNote
        {
            public int keyIndex;
            public float start;
            public float end;
            public RaylibColor colorLeft;
            public RaylibColor colorRight;
        }
    }
}
