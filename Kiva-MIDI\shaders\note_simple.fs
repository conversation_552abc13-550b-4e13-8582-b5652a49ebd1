#version 330

// Inputs from vertex shader
in vec2 fragTexCoord;
in vec4 fragColor;

// Uniforms for note-specific data
uniform vec4 colorLeft;     // Left side color
uniform vec4 colorRight;    // Right side color
uniform vec2 noteSize;      // Note width and height in pixels
uniform vec2 screenRes;     // Screen resolution

// Output
out vec4 finalColor;

void main() {
    // Calculate border thickness exactly like the original shader (Notes.fx lines 51-53)
    float noteBorder = 0.00091;
    float noteBorderH = round(noteBorder * screenRes.x) / screenRes.x * screenRes.x;
    float noteBorderV = round(noteBorder * screenRes.y) / screenRes.y * screenRes.y / (screenRes.y / screenRes.x);
    
    // Ensure minimum border size (1 pixel)
    noteBorderH = max(1.0, noteBorderH);
    noteBorderV = max(1.0, noteBorderV);
    
    // Convert border from screen pixels to note-relative coordinates
    vec2 borderRel = vec2(noteBorderH / noteSize.x, noteBorderV / noteSize.y);
    
    // Determine if we're in the border area or inner area
    bool inBorder = (fragTexCoord.x < borderRel.x || fragTexCoord.x > (1.0 - borderRel.x) ||
                     fragTexCoord.y < borderRel.y || fragTexCoord.y > (1.0 - borderRel.y));
    
    // Extract original colors
    vec4 colorL = colorLeft;
    vec4 colorR = colorRight;
    
    // Apply alpha transformation (matching Notes.fx lines 45-46)
    colorL.w *= colorL.w;
    colorL.w *= colorL.w;
    
    if (inBorder) {
        // Step 1: Draw dark background/shadow (matching shader lines 55-60)
        // cl.xyz *= 0.2f; cl.xyz -= 0.05f; cl.xyz = clamp(cl.xyz, 0, 1);
        vec4 shadowColorL = vec4(
            clamp(colorL.rgb * 0.2 - 0.05, 0.0, 1.0),
            colorL.a
        );
        vec4 shadowColorR = vec4(
            clamp(colorR.rgb * 0.2 - 0.05, 0.0, 1.0),
            colorR.a
        );
        
        // Interpolate horizontally for gradient effect
        finalColor = mix(shadowColorL, shadowColorR, fragTexCoord.x);
    } else {
        // Step 2: Draw bright inner area (matching shader lines 88-93)
        // cl.xyz += 0.1f; cr.xyz -= 0.3f; cl.xyz = clamp(cl.xyz, 0, 1);
        vec4 innerColorL = vec4(
            clamp(colorL.rgb + 0.1, 0.0, 1.0),
            colorL.a
        );
        vec4 innerColorR = vec4(
            clamp(colorR.rgb - 0.3, 0.0, 1.0),
            colorR.a
        );
        
        // Interpolate horizontally for gradient effect
        finalColor = mix(innerColorL, innerColorR, fragTexCoord.x);
    }
}
