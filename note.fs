#version 330

// Input from vertex shader
in vec2 fragTexCoord;
in vec4 fragColorL;
in vec4 fragColorR;
in vec2 notePos;

// Output
out vec4 finalColor;

// Uniforms
uniform sampler2D texture0;
uniform vec4 colDiffuse;
uniform vec2 screenSize;
uniform float noteBorder;

void main()
{
    // Calculate border thickness (matching original shader logic)
    float noteBorderH = round(noteBorder * screenSize.x) / screenSize.x;
    float noteBorderV = round(noteBorder * screenSize.y) / screenSize.y / (screenSize.y / screenSize.x);
    
    // Ensure minimum border size
    noteBorderH = max(1.0 / screenSize.x, noteBorderH);
    noteBorderV = max(1.0 / screenSize.y, noteBorderV);
    
    // Check if we're in the border area
    bool inBorder = (notePos.x < noteBorderH || notePos.x > (1.0 - noteBorderH) ||
                     notePos.y < noteBorderV || notePos.y > (1.0 - noteBorderV));
    
    vec4 color;
    
    if (inBorder) {
        // Border/shadow area - darker colors (matching original shader lines 55-60)
        vec4 shadowColorL = fragColorL;
        vec4 shadowColorR = fragColorR;
        
        // Apply shadow effect: cl.xyz *= 0.2f; cl.xyz -= 0.05f; cl.xyz = clamp(cl.xyz, 0, 1);
        shadowColorL.rgb = clamp(shadowColorL.rgb * 0.2 - 0.05, 0.0, 1.0);
        shadowColorR.rgb = clamp(shadowColorR.rgb * 0.2 - 0.05, 0.0, 1.0);
        
        // Horizontal gradient for shadow
        float t = notePos.x;
        color = mix(shadowColorL, shadowColorR, t);
    } else {
        // Inner area - brighter colors (matching original shader lines 88-93)
        vec4 innerColorL = fragColorL;
        vec4 innerColorR = fragColorR;
        
        // Apply inner effect: cl.xyz += 0.1f; cr.xyz -= 0.3f; cl.xyz = clamp(cl.xyz, 0, 1);
        innerColorL.rgb = clamp(innerColorL.rgb + 0.1, 0.0, 1.0);
        innerColorR.rgb = clamp(innerColorR.rgb - 0.3, 0.0, 1.0);
        
        // Horizontal gradient for inner area
        float t = notePos.x;
        color = mix(innerColorL, innerColorR, t);
    }
    
    // Apply alpha from original colors (with alpha squaring effect from original shader)
    float alpha = fragColorL.a;
    alpha = alpha * alpha; // Square the alpha twice like original
    alpha = alpha * alpha;
    color.a = alpha;
    
    // Apply texture and diffuse color
    vec4 texColor = texture(texture0, fragTexCoord);
    finalColor = texColor * color * colDiffuse;
}
