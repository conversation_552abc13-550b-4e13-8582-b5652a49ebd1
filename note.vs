#version 330

// Input vertex attributes
in vec3 vertexPosition;
in vec2 vertexTexCoord;

// Input instance attributes (per-note data)
in vec4 noteRect;      // x, y, width, height in screen coordinates
in vec4 noteColorL;    // Left color (RGBA)
in vec4 noteColorR;    // Right color (RGBA)
in float noteLayer;    // Layer for depth sorting (0.0 = background, 1.0 = foreground)

// Output to fragment shader
out vec2 fragTexCoord;
out vec4 fragColorL;
out vec4 fragColorR;
out vec2 notePos;      // Position within the note (0,0 to 1,1)

// Uniforms
uniform mat4 mvp;
uniform vec2 screenSize;
uniform float noteBorder;

void main()
{
    // Calculate note position in screen space
    vec2 noteSize = noteRect.zw;
    vec2 noteCenter = noteRect.xy + noteSize * 0.5;
    
    // Transform vertex position to note-local coordinates
    vec2 localPos = vertexPosition.xy; // Assumes quad vertices are in [-0.5, 0.5] range
    vec2 worldPos = noteCenter + localPos * noteSize;
    
    // Convert to normalized device coordinates
    vec2 ndc = (worldPos / screenSize) * 2.0 - 1.0;
    ndc.y = -ndc.y; // Flip Y coordinate for screen space
    
    // Set position with depth for layer sorting
    gl_Position = vec4(ndc, noteLayer * 0.01, 1.0); // Small depth difference between layers
    
    // Pass through texture coordinates and colors
    fragTexCoord = vertexTexCoord;
    fragColorL = noteColorL;
    fragColorR = noteColorR;
    
    // Calculate position within note for gradient and border effects
    notePos = vertexPosition.xy + 0.5; // Convert from [-0.5, 0.5] to [0, 1]
}
