using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace Kiva_MIDI
{
    /// <summary>
    /// SecondPlayer that renders MIDI notes to a texture buffer for visualization.
    /// Runs consistently in the background, starting from the main player's current position.
    /// Buffer Health: 32768 samples ahead of current time.
    /// Restarts on seeks or speed changes.
    /// </summary>
    public class MIDISecondPlayer : IDisposable
    {
        private const int BUFFER_HEALTH = 32768; // Buffer health in samples (time units ahead)
        private const int TEXTURE_WIDTH = 2048;  // Width of the texture buffer
        private const int TEXTURE_HEIGHT = 4096; // Height of the texture buffer (scrolling buffer)
        
        private Settings settings;
        private MIDIFile file;
        private PlayingState time;
        private RaylibRenderer renderer;
        
        // Texture rendering
        private RenderTexture2D renderTexture;
        private bool textureInitialized = false;
        private int textureWidth;
        private int textureHeight;
        
        // Threading and control
        private Task renderingThread;
        private CancellationTokenSource cancellationTokenSource;
        private volatile bool disposed = false;
        private volatile bool restartRequested = false;
        
        // Buffer management
        private double bufferStartTime = 0;
        private double bufferEndTime = 0;
        private double currentRenderTime = 0;
        private readonly object bufferLock = new object();

        // Texture scrolling management
        private float textureScrollOffset = 0;
        private float pixelsPerSecond = 100.0f; // How many pixels represent one second
        
        // Rendering state
        private double lastMainPlayerTime = 0;
        private double lastSpeed = 1.0;
        private bool isRendering = false;

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (bufferLock)
                {
                    file = value;
                    RequestRestart();
                }
            }
        }

        public PlayingState Time
        {
            get => time;
            set
            {
                if (time != null)
                {
                    time.TimeChanged -= OnTimeChanged;
                    time.SpeedChanged -= OnSpeedChanged;
                }
                time = value;
                if (time != null)
                {
                    time.TimeChanged += OnTimeChanged;
                    time.SpeedChanged += OnSpeedChanged;
                }
            }
        }

        public RenderTexture2D RenderTexture => renderTexture;
        public bool IsTextureReady => textureInitialized && renderTexture.id != 0;

        /// <summary>
        /// Gets the current texture region that should be displayed based on the main player's time
        /// </summary>
        public Rectangle GetCurrentTextureRegion(int displayWidth, int displayHeight)
        {
            if (!IsTextureReady || time == null)
                return new Rectangle(0, 0, displayWidth, displayHeight);

            double currentTime = time.GetTime();
            double timeIntoBuffer = currentTime - bufferStartTime;

            // Calculate the Y offset in the texture based on time
            float yOffset = (float)(timeIntoBuffer * pixelsPerSecond) % textureHeight;

            return new Rectangle(0, yOffset, Math.Min(displayWidth, textureWidth), Math.Min(displayHeight, textureHeight - yOffset));
        }

        public MIDISecondPlayer(Settings settings, int textureWidth = TEXTURE_WIDTH, int textureHeight = TEXTURE_HEIGHT)
        {
            this.settings = settings;
            this.textureWidth = textureWidth;
            this.textureHeight = textureHeight;
            
            // Create a dedicated renderer for texture rendering
            renderer = new RaylibRenderer(settings);
            renderer.SetScreenSize(textureWidth, textureHeight);
            
            InitializeTexture();
            StartRenderingThread();
        }

        private void InitializeTexture()
        {
            try
            {
                if (textureInitialized)
                {
                    Raylib.UnloadRenderTexture(renderTexture);
                }
                
                renderTexture = Raylib.LoadRenderTexture(textureWidth, textureHeight);
                textureInitialized = renderTexture.id != 0;
                
                if (textureInitialized)
                {
                    // Clear the texture initially
                    Raylib.BeginTextureMode(renderTexture);
                    Raylib.ClearBackground(RaylibColor.BLACK);
                    Raylib.EndTextureMode();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize SecondPlayer texture: {ex.Message}");
                textureInitialized = false;
            }
        }

        private void StartRenderingThread()
        {
            cancellationTokenSource = new CancellationTokenSource();
            renderingThread = Task.Run(() => RenderingLoop(cancellationTokenSource.Token), cancellationTokenSource.Token);
        }

        private void OnTimeChanged()
        {
            // Check if this is a seek operation (significant time jump)
            double currentTime = time?.GetTime() ?? 0;
            if (Math.Abs(currentTime - lastMainPlayerTime) > 0.1) // 100ms threshold for seek detection
            {
                RequestRestart();
            }
            lastMainPlayerTime = currentTime;
        }

        private void OnSpeedChanged()
        {
            double currentSpeed = time?.Speed ?? 1.0;
            if (Math.Abs(currentSpeed - lastSpeed) > 0.01) // Speed change threshold
            {
                RequestRestart();
                lastSpeed = currentSpeed;
            }
        }

        private void RequestRestart()
        {
            restartRequested = true;
        }

        private async void RenderingLoop(CancellationToken cancellationToken)
        {
            while (!disposed && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    if (file == null || time == null || !textureInitialized)
                    {
                        await Task.Delay(50, cancellationToken);
                        continue;
                    }

                    // Check if restart is requested
                    if (restartRequested)
                    {
                        RestartBuffer();
                        restartRequested = false;
                    }

                    // Check buffer health and render if needed
                    double mainPlayerTime = time.GetTime();
                    double bufferHealthTime = BUFFER_HEALTH / 1000.0; // Convert to seconds (assuming 1000 samples per second)
                    
                    lock (bufferLock)
                    {
                        // Check if we need to render more content
                        if (bufferEndTime < mainPlayerTime + bufferHealthTime)
                        {
                            RenderNextChunk(mainPlayerTime, bufferHealthTime);
                        }
                    }

                    // Small delay to prevent excessive CPU usage
                    await Task.Delay(16, cancellationToken); // ~60 FPS update rate
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"SecondPlayer rendering error: {ex.Message}");
                    await Task.Delay(100, cancellationToken);
                }
            }
        }

        private void RestartBuffer()
        {
            lock (bufferLock)
            {
                double mainPlayerTime = time?.GetTime() ?? 0;
                bufferStartTime = mainPlayerTime;
                bufferEndTime = mainPlayerTime;
                currentRenderTime = mainPlayerTime;
                
                // Clear the texture
                if (textureInitialized)
                {
                    Raylib.BeginTextureMode(renderTexture);
                    Raylib.ClearBackground(RaylibColor.BLACK);
                    Raylib.EndTextureMode();
                }
            }
        }

        private void RenderNextChunk(double mainPlayerTime, double bufferHealthTime)
        {
            if (!textureInitialized || file == null)
                return;

            try
            {
                isRendering = true;

                // Calculate the time range to render
                double renderStartTime = Math.Max(currentRenderTime, mainPlayerTime);
                double renderEndTime = mainPlayerTime + bufferHealthTime;
                double timeScale = settings?.Volatile?.Size ?? 1.0;

                // Calculate how much new content we need to render
                double newContentDuration = renderEndTime - currentRenderTime;
                if (newContentDuration <= 0)
                    return;

                // Begin rendering to texture
                Raylib.BeginTextureMode(renderTexture);

                // Calculate the Y position in texture where new content should be rendered
                double timeIntoBuffer = currentRenderTime - bufferStartTime;
                float renderYStart = (float)(timeIntoBuffer * pixelsPerSecond) % textureHeight;
                float renderHeight = (float)(newContentDuration * pixelsPerSecond);

                // Clear the region we're about to render to
                Raylib.DrawRectangle(0, (int)renderYStart, textureWidth, (int)Math.Min(renderHeight, textureHeight - renderYStart), RaylibColor.BLACK);

                // Set up renderer for this frame
                renderer.BeginFrame();

                // Render MIDI notes for the time range with proper Y offset
                RenderMIDINotesToTexture(renderStartTime, renderEndTime, timeScale, renderYStart);

                // End texture rendering
                Raylib.EndTextureMode();

                // Update buffer state
                currentRenderTime = renderEndTime;
                bufferEndTime = renderEndTime;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering MIDI chunk to texture: {ex.Message}");
            }
            finally
            {
                isRendering = false;
            }
        }

        private void RenderMIDINotesToTexture(double startTime, double endTime, double timeScale, float yOffset = 0)
        {
            if (!(file is MIDIMemoryFile memoryFile))
                return;

            try
            {
                // Render notes for each channel
                for (int channel = 0; channel < memoryFile.Notes.Length; channel++)
                {
                    var notes = memoryFile.Notes[channel];
                    if (notes == null || notes.Length == 0)
                        continue;

                    RenderChannelNotes(notes, channel, startTime, endTime, timeScale, yOffset, memoryFile);
                }

                // Render the accumulated notes
                renderer.RenderNotes();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error rendering MIDI notes to texture: {ex.Message}");
            }
        }

        private void RenderChannelNotes(Note[] notes, int channel, double startTime, double endTime, double timeScale, float yOffset, MIDIMemoryFile memoryFile)
        {
            var colors = memoryFile.Colors;

            foreach (var note in notes)
            {
                // Check if note is within the render time range
                if (note.end < startTime || note.start > endTime)
                    continue;

                // Calculate note position in texture coordinates
                // Convert time to pixel position and add the Y offset for scrolling buffer
                float noteStart = (float)((note.start - startTime) * pixelsPerSecond / textureHeight) + yOffset / textureHeight;
                float noteEnd = (float)((note.end - startTime) * pixelsPerSecond / textureHeight) + yOffset / textureHeight;

                // Wrap around if we exceed texture bounds
                if (noteStart > 1.0f) noteStart -= 1.0f;
                if (noteEnd > 1.0f) noteEnd -= 1.0f;

                // Get note color
                var noteColor = ColorFromNoteCol(colors[note.colorPointer]);

                // Add note to renderer buffer using proper piano key layout
                renderer.AddNoteByKey(note.note, noteStart, noteEnd, noteColor, noteColor);
            }
        }

        private RaylibColor ColorFromNoteCol(NoteCol noteCol)
        {
            return new RaylibColor(
                (byte)((noteCol.rgba >> 24) & 0xFF),
                (byte)((noteCol.rgba >> 16) & 0xFF),
                (byte)((noteCol.rgba >> 8) & 0xFF),
                (byte)(noteCol.rgba & 0xFF)
            );
        }

        public void Dispose()
        {
            if (disposed)
                return;

            disposed = true;
            
            // Stop the rendering thread
            cancellationTokenSource?.Cancel();
            renderingThread?.Wait(1000);
            
            // Clean up texture
            if (textureInitialized)
            {
                Raylib.UnloadRenderTexture(renderTexture);
                textureInitialized = false;
            }
            
            // Clean up renderer
            renderer?.Dispose();
            
            // Unsubscribe from events
            if (time != null)
            {
                time.TimeChanged -= OnTimeChanged;
                time.SpeedChanged -= OnSpeedChanged;
            }
            
            cancellationTokenSource?.Dispose();
        }
    }
}
