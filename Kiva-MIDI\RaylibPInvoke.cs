using System;
using System.Runtime.InteropServices;

namespace Kiva_MIDI
{
    /// <summary>
    /// Direct P/Invoke bindings for raylib - simplified version for basic functionality
    /// This avoids the .NET Standard compatibility issues with Raylib-cs
    /// </summary>
    public static class RaylibPInvoke
    {
        private const string RAYLIB_DLL = "raylib.dll";

        [StructLayout(LayoutKind.Sequential)]
        public struct Color
        {
            public byte r;
            public byte g;
            public byte b;
            public byte a;

            public Color(byte r, byte g, byte b, byte a)
            {
                this.r = r;
                this.g = g;
                this.b = b;
                this.a = a;
            }

            public static readonly Color BLACK = new Color(0, 0, 0, 255);
            public static readonly Color WHITE = new Color(255, 255, 255, 255);
            public static readonly Color RED = new Color(255, 0, 0, 255);
            public static readonly Color GREEN = new Color(0, 255, 0, 255);
            public static readonly Color BLUE = new Color(0, 0, 255, 255);
            public static readonly Color YELLOW = new Color(255, 255, 0, 255);
            public static readonly Color GRAY = new Color(128, 128, 128, 255);
            public static readonly Color DARKGRAY = new Color(80, 80, 80, 255);
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Vector2
        {
            public float x;
            public float y;

            public Vector2(float x, float y)
            {
                this.x = x;
                this.y = y;
            }
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Font
        {
            public int baseSize;
            public int glyphCount;
            public int glyphPadding;
            public IntPtr texture;
            public IntPtr recs;
            public IntPtr glyphs;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Shader
        {
            public uint id;
            public IntPtr locs;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Mesh
        {
            public int vertexCount;
            public int triangleCount;
            public IntPtr vertices;
            public IntPtr texcoords;
            public IntPtr texcoords2;
            public IntPtr normals;
            public IntPtr tangents;
            public IntPtr colors;
            public IntPtr indices;
            public IntPtr animVertices;
            public IntPtr animNormals;
            public IntPtr boneIds;
            public IntPtr boneWeights;
            public uint vaoId;
            public IntPtr vboId;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Material
        {
            public Shader shader;
            public IntPtr maps;
            public IntPtr @params;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Matrix4x4
        {
            public float m0, m4, m8, m12;
            public float m1, m5, m9, m13;
            public float m2, m6, m10, m14;
            public float m3, m7, m11, m15;

            public static Matrix4x4 Identity => new Matrix4x4
            {
                m0 = 1.0f, m4 = 0.0f, m8 = 0.0f, m12 = 0.0f,
                m1 = 0.0f, m5 = 1.0f, m9 = 0.0f, m13 = 0.0f,
                m2 = 0.0f, m6 = 0.0f, m10 = 1.0f, m14 = 0.0f,
                m3 = 0.0f, m7 = 0.0f, m11 = 0.0f, m15 = 1.0f
            };
        }

        [Flags]
        public enum ConfigFlags
        {
            FLAG_VSYNC_HINT = 0x00000040,
            FLAG_FULLSCREEN_MODE = 0x00000002,
            FLAG_WINDOW_RESIZABLE = 0x00000004,
            FLAG_WINDOW_UNDECORATED = 0x00000008,
            FLAG_WINDOW_HIDDEN = 0x00000080,
            FLAG_WINDOW_MINIMIZED = 0x00000200,
            FLAG_WINDOW_MAXIMIZED = 0x00000400,
            FLAG_WINDOW_UNFOCUSED = 0x00000800,
            FLAG_WINDOW_TOPMOST = 0x00001000,
            FLAG_WINDOW_ALWAYS_RUN = 0x00000100,
            FLAG_WINDOW_TRANSPARENT = 0x00000010,
            FLAG_WINDOW_HIGHDPI = 0x00002000,
            FLAG_MSAA_4X_HINT = 0x00000020,
            FLAG_INTERLACED_HINT = 0x00010000
        }

        // Window management
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetConfigFlags(ConfigFlags flags);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void InitWindow(int width, int height, string title);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ToggleFullscreen();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsWindowFullscreen();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMonitorWidth(int monitor);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMonitorHeight(int monitor);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool WindowShouldClose();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void CloseWindow();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsWindowReady();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetTargetFPS(int fps);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetScreenWidth();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetScreenHeight();

        // Drawing
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void BeginDrawing();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void EndDrawing();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ClearBackground(Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawRectangle(int posX, int posY, int width, int height, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawRectangleLines(int posX, int posY, int width, int height, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawLine(int startPosX, int startPosY, int endPosX, int endPosY, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawCircle(int centerX, int centerY, float radius, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawCircleLines(int centerX, int centerY, float radius, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawText(string text, int posX, int posY, int fontSize, Color color);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern Font LoadFont(string fileName);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawTextEx(Font font, string text, Vector2 position, float fontSize, float spacing, Color tint);

        // Shader functions for GPU acceleration
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern Shader LoadShader(string vsFileName, string fsFileName);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadShader(Shader shader);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void BeginShaderMode(Shader shader);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void EndShaderMode();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
        public static extern int GetShaderLocation(Shader shader, string uniformName);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetShaderValue(Shader shader, int locIndex, IntPtr value, int uniformType);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetShaderValueV(Shader shader, int locIndex, IntPtr value, int uniformType, int count);

        // Mesh and vertex buffer functions for instanced rendering
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern Mesh GenMeshPlane(float width, float length, int resX, int resZ);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawMesh(Mesh mesh, Material material, Matrix4x4 transform);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void DrawMeshInstanced(Mesh mesh, Material material, IntPtr transforms, int instances);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadMesh(Mesh mesh);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern Material LoadMaterialDefault();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern void UnloadMaterial(Material material);

        // Input
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsKeyPressed(int key);



        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsKeyDown(int key);

        // Mouse input
        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonPressed(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonDown(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsMouseButtonReleased(int button);

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMouseX();

        [DllImport(RAYLIB_DLL, CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetMouseY();

        // Key codes
        public const int KEY_ESCAPE = 256;
        public const int KEY_SPACE = 32;
        public const int KEY_ENTER = 257;
        public const int KEY_BACKSPACE = 259;
        public const int KEY_DELETE = 261;
        public const int KEY_TAB = 258;

        // Arrow keys
        public const int KEY_UP = 265;
        public const int KEY_DOWN = 264;
        public const int KEY_LEFT = 263;
        public const int KEY_RIGHT = 262;

        // Modifier keys
        public const int KEY_LEFT_SHIFT = 340;
        public const int KEY_RIGHT_SHIFT = 344;

        // Letter keys (ASCII values)
        public const int KEY_O = 79;
        public const int KEY_P = 80;
        public const int KEY_S = 83;
        public const int KEY_H = 72;
        public const int KEY_I = 73;

        // Number keys (ASCII values)
        public const int KEY_0 = 48;
        public const int KEY_1 = 49;
        public const int KEY_2 = 50;
        public const int KEY_3 = 51;
        public const int KEY_4 = 52;
        public const int KEY_5 = 53;
        public const int KEY_6 = 54;
        public const int KEY_7 = 55;
        public const int KEY_8 = 56;
        public const int KEY_9 = 57;

        // Special characters
        public const int KEY_PERIOD = 46; // '.' key

        // Mouse buttons
        public const int MOUSE_BUTTON_LEFT = 0;
        public const int MOUSE_BUTTON_RIGHT = 1;
        public const int MOUSE_BUTTON_MIDDLE = 2;

        // Shader uniform types
        public const int SHADER_UNIFORM_FLOAT = 0;
        public const int SHADER_UNIFORM_VEC2 = 1;
        public const int SHADER_UNIFORM_VEC3 = 2;
        public const int SHADER_UNIFORM_VEC4 = 3;
        public const int SHADER_UNIFORM_INT = 4;
        public const int SHADER_UNIFORM_IVEC2 = 5;
        public const int SHADER_UNIFORM_IVEC3 = 6;
        public const int SHADER_UNIFORM_IVEC4 = 7;
        public const int SHADER_UNIFORM_SAMPLER2D = 8;

        // Helper methods for easier usage
        public static class Raylib
        {
            public static void InitWindow(int width, int height, string title) => RaylibPInvoke.InitWindow(width, height, title);
            public static bool WindowShouldClose() => RaylibPInvoke.WindowShouldClose();
            public static void CloseWindow() => RaylibPInvoke.CloseWindow();
            public static bool IsWindowReady() => RaylibPInvoke.IsWindowReady();
            public static void SetTargetFPS(int fps) => RaylibPInvoke.SetTargetFPS(fps);
            public static int GetScreenWidth() => RaylibPInvoke.GetScreenWidth();
            public static int GetScreenHeight() => RaylibPInvoke.GetScreenHeight();
            public static void BeginDrawing() => RaylibPInvoke.BeginDrawing();
            public static void EndDrawing() => RaylibPInvoke.EndDrawing();
            public static void ClearBackground(Color color) => RaylibPInvoke.ClearBackground(color);
            public static void DrawRectangle(int posX, int posY, int width, int height, Color color) => RaylibPInvoke.DrawRectangle(posX, posY, width, height, color);
            public static void DrawRectangleLines(int posX, int posY, int width, int height, Color color) => RaylibPInvoke.DrawRectangleLines(posX, posY, width, height, color);
            public static void DrawLine(int startPosX, int startPosY, int endPosX, int endPosY, Color color) => RaylibPInvoke.DrawLine(startPosX, startPosY, endPosX, endPosY, color);
            public static void DrawCircle(int centerX, int centerY, float radius, Color color) => RaylibPInvoke.DrawCircle(centerX, centerY, radius, color);
            public static void DrawCircleLines(int centerX, int centerY, float radius, Color color) => RaylibPInvoke.DrawCircleLines(centerX, centerY, radius, color);
            public static void DrawText(string text, int posX, int posY, int fontSize, Color color) => RaylibPInvoke.DrawText(text, posX, posY, fontSize, color);
            public static Font LoadFont(string fileName) => RaylibPInvoke.LoadFont(fileName);
            public static void DrawTextEx(Font font, string text, Vector2 position, float fontSize, float spacing, Color tint) => RaylibPInvoke.DrawTextEx(font, text, position, fontSize, spacing, tint);
            public static bool IsKeyPressed(int key) => RaylibPInvoke.IsKeyPressed(key);
            public static bool IsKeyDown(int key) => RaylibPInvoke.IsKeyDown(key);
            public static bool IsMouseButtonPressed(int button) => RaylibPInvoke.IsMouseButtonPressed(button);
            public static bool IsMouseButtonDown(int button) => RaylibPInvoke.IsMouseButtonDown(button);
            public static bool IsMouseButtonReleased(int button) => RaylibPInvoke.IsMouseButtonReleased(button);
            public static int GetMouseX() => RaylibPInvoke.GetMouseX();
            public static int GetMouseY() => RaylibPInvoke.GetMouseY();
            public static void SetConfigFlags(ConfigFlags flags) => RaylibPInvoke.SetConfigFlags(flags);
            public static void ToggleFullscreen() => RaylibPInvoke.ToggleFullscreen();
            public static bool IsWindowFullscreen() => RaylibPInvoke.IsWindowFullscreen();
            public static int GetMonitorWidth(int monitor) => RaylibPInvoke.GetMonitorWidth(monitor);
            public static int GetMonitorHeight(int monitor) => RaylibPInvoke.GetMonitorHeight(monitor);

            // Shader functions
            public static Shader LoadShader(string vsFileName, string fsFileName) => RaylibPInvoke.LoadShader(vsFileName, fsFileName);
            public static void UnloadShader(Shader shader) => RaylibPInvoke.UnloadShader(shader);
            public static void BeginShaderMode(Shader shader) => RaylibPInvoke.BeginShaderMode(shader);
            public static void EndShaderMode() => RaylibPInvoke.EndShaderMode();
            public static int GetShaderLocation(Shader shader, string uniformName) => RaylibPInvoke.GetShaderLocation(shader, uniformName);
            public static void SetShaderValue(Shader shader, int locIndex, IntPtr value, int uniformType) => RaylibPInvoke.SetShaderValue(shader, locIndex, value, uniformType);
            public static void SetShaderValueV(Shader shader, int locIndex, IntPtr value, int uniformType, int count) => RaylibPInvoke.SetShaderValueV(shader, locIndex, value, uniformType, count);

            // Mesh functions
            public static Mesh GenMeshPlane(float width, float length, int resX, int resZ) => RaylibPInvoke.GenMeshPlane(width, length, resX, resZ);
            public static void DrawMesh(Mesh mesh, Material material, Matrix4x4 transform) => RaylibPInvoke.DrawMesh(mesh, material, transform);
            public static void DrawMeshInstanced(Mesh mesh, Material material, IntPtr transforms, int instances) => RaylibPInvoke.DrawMeshInstanced(mesh, material, transforms, instances);
            public static void UnloadMesh(Mesh mesh) => RaylibPInvoke.UnloadMesh(mesh);
            public static Material LoadMaterialDefault() => RaylibPInvoke.LoadMaterialDefault();
            public static void UnloadMaterial(Material material) => RaylibPInvoke.UnloadMaterial(material);
        }
    }
}
