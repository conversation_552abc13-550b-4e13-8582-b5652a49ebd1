using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Kiva_MIDI
{
    /// <summary>
    /// Second player for fast pre-rendering notes to texture only (no audio output)
    /// Similar to the preprocessing system in midi_visualizer.c
    /// </summary>
    public class MIDITexturePreRenderPlayer : IDisposable
    {
        private Settings settings;
        private MIDIFile file;
        private PlayingState mainPlayerTime;
        private RaylibRenderer renderer;
        private bool disposed = false;
        private CancellationTokenSource cancellationToken;
        private Task preRenderTask;
        private object fileLock = new object();

        // Pre-rendering state
        private double lastPreRenderTime = -1;
        private double preRenderSpeed = 10.0; // Render at 10x speed for fast texture updates
        private const double PRERENDER_BUFFER_SECONDS = 2.0; // How far ahead to pre-render

        public MIDIFile File
        {
            get => file;
            set
            {
                lock (fileLock)
                {
                    file = value;
                    RestartPreRendering();
                }
            }
        }

        public PlayingState MainPlayerTime
        {
            get => mainPlayerTime;
            set
            {
                if (mainPlayerTime != null)
                {
                    mainPlayerTime.TimeChanged -= OnMainPlayerTimeChanged;
                    mainPlayerTime.SpeedChanged -= OnMainPlayerSpeedChanged;
                }
                
                mainPlayerTime = value;
                
                if (mainPlayerTime != null)
                {
                    mainPlayerTime.TimeChanged += OnMainPlayerTimeChanged;
                    mainPlayerTime.SpeedChanged += OnMainPlayerSpeedChanged;
                }
            }
        }

        public RaylibRenderer Renderer
        {
            get => renderer;
            set => renderer = value;
        }

        public MIDITexturePreRenderPlayer(Settings settings, RaylibRenderer renderer)
        {
            this.settings = settings;
            this.renderer = renderer;
            this.cancellationToken = new CancellationTokenSource();
        }

        private void OnMainPlayerTimeChanged()
        {
            // When main player seeks, restart texture buffer
            RestartPreRendering();
        }

        private void OnMainPlayerSpeedChanged()
        {
            // When note speed changes, recalculate and re-render texture
            RestartPreRendering();
        }

        private void RestartPreRendering()
        {
            if (disposed || file == null || mainPlayerTime == null)
                return;

            // Cancel current pre-rendering
            cancellationToken?.Cancel();
            preRenderTask?.Wait(100); // Wait briefly for cancellation

            // Start new pre-rendering task
            cancellationToken = new CancellationTokenSource();
            preRenderTask = Task.Run(() => PreRenderTextureAsync(cancellationToken.Token));
        }

        private async Task PreRenderTextureAsync(CancellationToken token)
        {
            try
            {
                while (!token.IsCancellationRequested && !disposed)
                {
                    lock (fileLock)
                    {
                        if (file == null || mainPlayerTime == null || renderer == null)
                        {
                            await Task.Delay(100, token);
                            continue;
                        }

                        double currentTime = mainPlayerTime.GetTime();
                        double noteSpeed = settings?.Volatile?.Size ?? 1.0;

                        // Only pre-render if we need to update
                        if (Math.Abs(currentTime - lastPreRenderTime) > 0.1) // Update every 100ms
                        {
                            PreRenderNotesToTexture(currentTime, noteSpeed);
                            lastPreRenderTime = currentTime;
                        }
                    }

                    // Fast update rate for smooth texture scrolling
                    await Task.Delay(16, token); // ~60 FPS update rate
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancelling
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Pre-render error: {ex.Message}");
            }
        }

        private void PreRenderNotesToTexture(double currentTime, double noteSpeed)
        {
            if (!(file is MIDIMemoryFile memoryFile) || renderer == null)
                return;

            try
            {
                // Set color events for current time
                memoryFile.SetColorEvents(currentTime);
                var colors = memoryFile.MidiNoteColors;
                if (colors == null) return;

                // Fast pre-render notes to texture (similar to midi_visualizer.c preprocessing)
                double renderCutoff = currentTime + (PRERENDER_BUFFER_SECONDS * noteSpeed);

                // Process all notes and update texture states
                for (int k = 0; k < Math.Min(128, memoryFile.Notes.Length); k++)
                {
                    var notes = memoryFile.Notes[k];
                    if (notes == null || notes.Length == 0) continue;

                    // Find active or recently active notes for this key
                    bool noteActive = false;
                    float noteStartTime = 0;
                    float noteReleaseTime = -1;
                    int noteVelocity = 0;
                    var noteColor = RaylibPInvoke.Color.WHITE;
                    var outlineColor = RaylibPInvoke.Color.BLACK;

                    foreach (var note in notes)
                    {
                        // Skip notes that are too far in the past
                        if (note.end < currentTime - 0.5) continue;
                        // Skip notes that are too far in the future
                        if (note.start > renderCutoff) break;

                        // Check if note is currently active
                        if (note.start <= currentTime && note.end >= currentTime)
                        {
                            noteActive = true;
                            noteStartTime = (float)note.start;
                            noteVelocity = note.velocity;

                            // Get note color
                            var noteCol = colors[note.colorPointer];
                            noteColor = new RaylibPInvoke.Color(
                                (byte)((noteCol.rgba >> 24) & 0xFF),
                                (byte)((noteCol.rgba >> 16) & 0xFF),
                                (byte)((noteCol.rgba >> 8) & 0xFF),
                                (byte)(noteCol.rgba & 0xFF)
                            );
                            outlineColor = DarkenColor(noteColor, 0.35f);
                        }
                        else if (note.end < currentTime && note.end > currentTime - 0.5)
                        {
                            // Recently released note
                            noteReleaseTime = (float)note.end;
                        }
                    }

                    // Update note state for texture rendering
                    renderer.UpdateNoteState(k, noteActive, noteStartTime, noteReleaseTime,
                        0, noteVelocity, 0, noteColor, outlineColor);
                }

                // Update the texture with current time and note speed
                renderer.UpdateNoteTexture((float)currentTime, noteSpeed);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in PreRenderNotesToTexture: {ex.Message}");
            }
        }

        private RaylibPInvoke.Color DarkenColor(RaylibPInvoke.Color color, float amount)
        {
            return new RaylibPInvoke.Color(
                (byte)(color.r * amount),
                (byte)(color.g * amount),
                (byte)(color.b * amount),
                color.a
            );
        }

        public void Dispose()
        {
            if (disposed) return;
            disposed = true;

            cancellationToken?.Cancel();
            preRenderTask?.Wait(1000); // Wait up to 1 second for cleanup

            cancellationToken?.Dispose();
            preRenderTask?.Dispose();
        }
    }
}
