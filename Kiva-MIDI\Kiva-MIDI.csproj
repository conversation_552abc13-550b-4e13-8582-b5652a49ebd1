﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\PropertyChanged.Fody.3.1.3\build\PropertyChanged.Fody.props" Condition="Exists('..\packages\PropertyChanged.Fody.3.1.3\build\PropertyChanged.Fody.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{47B2D2B8-530D-484C-955C-DBA0472AE45B}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>Kiva_MIDI</RootNamespace>
    <AssemblyName>Kiva</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <UseVSHostingProcess>true</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>Kiva_MIDI.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Bass.Net">
      <HintPath>.\Bass.Net.dll</HintPath>
    </Reference>
    <Reference Include="CSCore, Version=1.2.1.2, Culture=neutral, PublicKeyToken=5a08f2b6f4415dea, processorArchitecture=MSIL">
      <HintPath>..\packages\CSCore.1.2.1.2\lib\net35-client\CSCore.dll</HintPath>
    </Reference>
    <Reference Include="Cyotek.Drawing.BitmapFont, Version=1.0.0.0, Culture=neutral, PublicKeyToken=58daa28b0b2de221, processorArchitecture=MSIL">
      <HintPath>..\packages\Cyotek.Drawing.BitmapFont.1.3.4\lib\net46\Cyotek.Drawing.BitmapFont.dll</HintPath>
    </Reference>
    <Reference Include="DiscordRPC, Version=1.0.121.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DiscordRichPresence.1.0.121\lib\net35\DiscordRPC.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PropertyChanged, Version=3.1.3.0, Culture=neutral, PublicKeyToken=ee3ee20bcf148ddd, processorArchitecture=MSIL">
      <HintPath>packages\PropertyChanged.Fody.3.1.3\lib\net40\PropertyChanged.dll</HintPath>
    </Reference>

    <Reference Include="Sanford.Multimedia.Midi, Version=6.6.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Sanford.Multimedia.Midi.6.6.0\lib\net20\Sanford.Multimedia.Midi.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL">
      <HintPath>packages\SharpDX.4.2.0\lib\net45\SharpDX.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.D3DCompiler, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL">
      <HintPath>packages\SharpDX.D3DCompiler.4.2.0\lib\net45\SharpDX.D3DCompiler.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Direct3D11, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL">
      <HintPath>packages\SharpDX.Direct3D11.4.2.0\lib\net45\SharpDX.Direct3D11.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Direct3D9, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL">
      <HintPath>packages\SharpDX.Direct3D9.4.2.0\lib\net45\SharpDX.Direct3D9.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.DXGI, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL">
      <HintPath>packages\SharpDX.DXGI.4.2.0\lib\net45\SharpDX.DXGI.dll</HintPath>
    </Reference>
    <Reference Include="SharpDX.Mathematics, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL">
      <HintPath>packages\SharpDX.Mathematics.4.2.0\lib\net45\SharpDX.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="netstandard" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="BASSMIDI.cs" />
    <Compile Include="LoudMaxStream.cs" />
    <Compile Include="Material.WPF\InfoIcon.xaml.cs">
      <DependentUpon>InfoIcon.xaml</DependentUpon>
    </Compile>
    <Compile Include="Material.WPF\HexColorPicker.xaml.cs">
      <DependentUpon>HexColorPicker.xaml</DependentUpon>
    </Compile>
    <Compile Include="Material.WPF\BBinding.cs" />
    <Compile Include="Material.WPF\BetterRadio.xaml.cs">
      <DependentUpon>BetterRadio.xaml</DependentUpon>
    </Compile>
    <Compile Include="Material.WPF\BetterCheckbox.xaml.cs">
      <DependentUpon>BetterCheckbox.xaml</DependentUpon>
    </Compile>
    <Compile Include="DX.WPF\D3D.cs" />
    <Compile Include="DX.WPF\D3D11.cs" />
    <Compile Include="DX.WPF\D3D9.cs" />
    <Compile Include="DX.WPF\DeviceUtil.cs" />
    <Compile Include="DisposeGroup.cs" />
    <Compile Include="DX.WPF\DXElement.cs" />
    <Compile Include="DX.WPF\DXImageSource.cs" />
    <Compile Include="DX.WPF\DXSharing.cs" />
    <Compile Include="DX.WPF\DXUtils.cs" />
    <Compile Include="FastList.cs" />
    <Compile Include="FPS.cs" />
    <Compile Include="DX.WPF\IDirect3D.cs" />
    <Compile Include="GeneralSettings.cs" />
    <Compile Include="KDMAPI.cs" />
    <Compile Include="Material.WPF\InplaceConverter.cs" />
    <Compile Include="MIDIAudio.cs" />
    <Compile Include="MIDIMemoryFile.cs" />
    <Compile Include="MIDIParseFile.cs" />
    <Compile Include="MIDIPlayer.cs" />
    <Compile Include="LoadingMidiForm.xaml.cs">
      <DependentUpon>LoadingMidiForm.xaml</DependentUpon>
    </Compile>
    <Compile Include="Material.WPF\BetterSlider.xaml.cs">
      <DependentUpon>BetterSlider.xaml</DependentUpon>
    </Compile>
    <Compile Include="MIDIParsing\ConsumingEnumerable.cs" />
    <Compile Include="Material.WPF\RippleEffectDecorator.cs" />
    <Compile Include="MIDILoaderSettings.cs" />
    <Compile Include="MIDIParsing\BufferByteReader.cs" />
    <Compile Include="Disposer.cs" />
    <Compile Include="MIDIFile.cs" />
    <Compile Include="MIDIParsing\MIDITrackParser.cs" />
    <Compile Include="MIDIParsing\SkipIterator.cs" />
    <Compile Include="MIDIPreRenderPlayer.cs" />
    <Compile Include="MIDISecondPlayer.cs" />
    <Compile Include="MIDIRenderer.cs" />
    <Compile Include="Material.WPF\NumberSelect.xaml.cs">
      <DependentUpon>NumberSelect.xaml</DependentUpon>
    </Compile>
    <Compile Include="PaletteSettings.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="RaylibPInvoke.cs" />
    <Compile Include="RaylibRenderer.cs" />
    <Compile Include="RaylibScene.cs" />
    <Compile Include="RaylibMainWindow.cs" />
    <Compile Include="TestRaylib.cs" />
    <Compile Include="Settings\AdvancedSettings.xaml.cs">
      <DependentUpon>AdvancedSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="Settings\KDMAPIAudioSettings.xaml.cs">
      <DependentUpon>KDMAPIAudioSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="Settings\MiscSettings.xaml.cs">
      <DependentUpon>MiscSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="PlayingState.cs" />
    <Compile Include="Scene.cs" />
    <Compile Include="MIDIParsing\TimedMerger.cs" />
    <Compile Include="Settings.cs" />
    <Compile Include="Settings\AudioSettings.xaml.cs">
      <DependentUpon>AudioSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="Settings\PreRenderAudioSettings.xaml.cs">
      <DependentUpon>PreRenderAudioSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="Settings\SettingsWindow.xaml.cs">
      <DependentUpon>SettingsWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Settings\VisualSettings.xaml.cs">
      <DependentUpon>VisualSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="Settings\WinMMAudioSettings.xaml.cs">
      <DependentUpon>WinMMAudioSettings.xaml</DependentUpon>
    </Compile>
    <Compile Include="ShaderManager.cs" />
    <Compile Include="Material.WPF\ValueSlider.xaml.cs">
      <DependentUpon>ValueSlider.xaml</DependentUpon>
    </Compile>
    <Compile Include="SoundfontSettings.cs" />
    <Compile Include="VelocityEase.cs" />
    <Compile Include="VolatileSettings.cs" />
    <Compile Include="WinMM.cs" />
    <Page Include="Material.WPF\InfoIcon.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Material.WPF\HexColorPicker.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Material.WPF\BetterRadio.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Material.WPF\BetterCheckbox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="LoadingMidiForm.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Material.WPF\BetterSlider.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Material.WPF\NumberSelect.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Scrollbar.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\AdvancedSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\KDMAPIAudioSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\MiscSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\AudioSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\PreRenderAudioSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\SettingsWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ButtonTemplates.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Material.WPF\ValueSlider.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\VisualSettings.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Settings\WinMMAudioSettings.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <Content Include="packages\Raylib-cs.3.1.5\runtimes\win-x64\native\raylib.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <Link>raylib.dll</Link>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Material.WPF\play.png" />
    <Resource Include="Material.WPF\pause.png" />
    <EmbeddedResource Include="Notes.fx" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="logo.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="kiva.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Material.WPF\settings.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Material.WPF\open.png" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="KeyboardSmall.fx" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="FodyWeavers.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="KeyboardBig.fx" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Material.WPF\infoIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.1 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\KivaShared\KivaShared.csproj">
      <Project>{181949ba-6424-4ad0-966b-1db6cdf1777d}</Project>
      <Name>KivaShared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="logo.ico" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bass.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="bassmidi.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\PropertyChanged.Fody.3.1.3\build\PropertyChanged.Fody.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\PropertyChanged.Fody.3.1.3\build\PropertyChanged.Fody.props'))" />
    <Error Condition="!Exists('..\packages\Fody.6.0.5\build\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Fody.6.0.5\build\Fody.targets'))" />
  </Target>
  <Target Name="AfterBuild">
    <ItemGroup>
      <MoveToLibFolder Include="$(OutputPath)*.dll ; $(OutputPath)*.pdb ; $(OutputPath)*.xml" />
    </ItemGroup>
    <Move SourceFiles="@(MoveToLibFolder)" DestinationFolder="$(OutputPath)lib" OverwriteReadOnlyFiles="true" />
  </Target>
  <Import Project="..\packages\Fody.6.0.5\build\Fody.targets" Condition="Exists('..\packages\Fody.6.0.5\build\Fody.targets')" />
</Project>